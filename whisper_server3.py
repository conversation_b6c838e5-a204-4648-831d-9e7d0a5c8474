from flask import Flask, request, jsonify
from flask_cors import CORS
from werkzeug.urls import quote
import whisper
import os
import tempfile
from opencc import OpenCC
import json
from datetime import datetime
import sys
import subprocess
from translate import Translator

# 检查并安装必要的依赖
try:
    from moviepy.video.io.VideoFileClip import VideoFileClip
except ImportError:
    print("正在安装必要的依赖...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "moviepy", "imageio-ffmpeg", "translate"])
    from moviepy.video.io.VideoFileClip import VideoFileClip

import numpy as np

# 检查ffmpeg是否可用
def check_ffmpeg():
    try:
        subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print("ffmpeg 已安装")
    except FileNotFoundError:
        print("警告: ffmpeg 未安装，视频处理功能可能无法使用")
        print("请安装 ffmpeg 以启用完整功能")

# 在应用启动时检查ffmpeg
check_ffmpeg()

app = Flask(__name__)
CORS(app)

# 加载Whisper模型
print("正在加载Whisper模型...")
model = whisper.load_model("medium")
print("Whisper模型加载完成")

# 初始化繁体转简体转换器
cc = OpenCC('t2s')  # 't2s'表示从繁体转换为简体

# 初始化翻译器
translator = Translator(to_lang="en", from_lang="zh")

# 历史记录文件路径
HISTORY_FILE = 'recognition_history.json'

# 支持的文件类型
ALLOWED_EXTENSIONS = {'mp3', 'wav', 'mp4', 'avi', 'mov', 'mkv'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def safe_filename(filename):
    """安全地处理文件名"""
    return quote(filename)

def extract_audio_from_video(video_path):
    """从视频文件中提取音频"""
    try:
        video = VideoFileClip(video_path)
        if video.audio is None:
            video.close()
            raise ValueError("视频文件没有音轨")
            
        # 创建临时文件来保存音频
        temp_audio = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
        temp_audio_path = temp_audio.name
        temp_audio.close()
        
        try:
            video.audio.write_audiofile(temp_audio_path)
            return temp_audio_path
        finally:
            video.close()
            
    except Exception as e:
        raise Exception(f"处理视频文件时出错: {str(e)}")

# 加载历史记录
def load_history():
    try:
        if os.path.exists(HISTORY_FILE):
            with open(HISTORY_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        print(f"加载历史记录失败: {str(e)}")
        return []

# 保存历史记录
def save_history(history):
    try:
        with open(HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存历史记录失败: {str(e)}")

@app.route('/process_media', methods=['POST'])
def process_media():
    temp_files = []  # 用于跟踪所有临时文件
    
    if 'file' not in request.files:
        return jsonify({'error': '没有上传文件'}), 400
    
    file = request.files['file']
    if not file:
        return jsonify({'error': '文件上传失败'}), 400

    if not allowed_file(file.filename):
        return jsonify({"error": "不支持的文件类型"}), 400

    try:
        # 安全处理文件名
        filename = safe_filename(file.filename)
        
        # 创建临时文件来保存上传的文件
        temp_input = tempfile.NamedTemporaryFile(delete=False)
        temp_files.append(temp_input.name)
        
        try:
            file.save(temp_input.name)
            temp_input.close()  # 确保文件被关闭
            
            # 如果是视频文件，先提取音频
            if filename.rsplit('.', 1)[1].lower() in {'mp4', 'avi', 'mov', 'mkv'}:
                try:
                    audio_path = extract_audio_from_video(temp_input.name)
                    temp_files.append(audio_path)
                except Exception as e:
                    return jsonify({"error": f"视频处理失败: {str(e)}"}), 500
            else:
                audio_path = temp_input.name
            
            try:
                # 使用Whisper进行中文识别
                print("开始识别...")
                result = model.transcribe(
                    audio_path,
                    language="zh",
                    task="transcribe",
                    beam_size=5,
                    best_of=5,
                    temperature=0.2,
                    condition_on_previous_text=True,
                    initial_prompt="这是一段清晰的中文语音。"
                )
                print("识别完成")

                # 处理识别结果，生成带时间戳的字幕
                subtitles = []
                for segment in result["segments"]:
                    # 转换繁体到简体
                    text_zh = cc.convert(segment["text"].strip())
                    # 翻译成英文
                    try:
                        text_en = translator.translate(text_zh)
                    except Exception as e:
                        print(f"翻译错误: {str(e)}")
                        text_en = "Translation failed"
                    
                    subtitles.append({
                        "start": segment["start"],
                        "end": segment["end"],
                        "text_zh": text_zh,
                        "text_en": text_en
                    })

                # 翻译完整文本
                try:
                    full_text_en = translator.translate(cc.convert(result['text']))
                except Exception as e:
                    print(f"完整文本翻译错误: {str(e)}")
                    full_text_en = "Translation failed"

                # 创建新的历史记录
                new_record = {
                    'id': datetime.now().strftime('%Y%m%d%H%M%S'),
                    'text_zh': cc.convert(result['text']),
                    'text_en': full_text_en,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'subtitles': subtitles,
                    'filename': filename
                }
                
                # 加载并更新历史记录
                history = load_history()
                history.insert(0, new_record)  # 在开头插入新记录
                save_history(history)
                
                return jsonify({
                    "success": True,
                    "subtitles": subtitles,
                    "text_zh": cc.convert(result['text']),
                    "text_en": full_text_en
                })
            
            finally:
                # 清理所有临时文件
                for temp_file in temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.close(os.open(temp_file, os.O_RDONLY))  # 确保文件句柄被关闭
                            os.unlink(temp_file)
                    except Exception as e:
                        print(f"清理临时文件失败: {str(e)}")
                
        except Exception as e:
            raise e
            
    except Exception as e:
        # 确保在发生错误时也清理临时文件
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.close(os.open(temp_file, os.O_RDONLY))
                    os.unlink(temp_file)
            except:
                pass
        print(f"错误: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/history', methods=['GET'])
def get_history():
    try:
        history = load_history()
        return jsonify(history)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/history/<record_id>', methods=['DELETE'])
def delete_history_record(record_id):
    try:
        history = load_history()
        history = [record for record in history if record['id'] != record_id]
        save_history(history)
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/history', methods=['DELETE'])
def clear_history():
    try:
        save_history([])
        return jsonify({'success': True})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001) 